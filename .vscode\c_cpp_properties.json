{"configurations": [{"name": "windows-gcc-x64", "includePath": ["${workspaceFolder}/**"], "compilerPath": "gcc", "cStandard": "${default}", "cppStandard": "${default}", "intelliSenseMode": "windows-gcc-x64", "compilerArgs": [""]}, {"name": "C:\\mingw64\\bin\\gcc.exe", "includePath": ["c:/Users/<USER>/Documents/C/**"], "defines": ["_DEBUG", "UNICODE", "_UNICODE"], "mergeConfigurations": false, "recursiveIncludes": {}, "browse": {"limitSymbolsToIncludedHeaders": true}, "cStandard": "c17"}, {"name": "C:\\msys64\\mingw64\\bin", "includePath": ["${workspaceFolder}/**"], "defines": ["_DEBUG", "UNICODE", "_UNICODE"]}, {"name": "C:\\msys64\\mingw64\\bin\\gcc.exe", "includePath": ["${workspaceFolder}/**"], "defines": ["_DEBUG", "UNICODE", "_UNICODE"]}], "version": 4}