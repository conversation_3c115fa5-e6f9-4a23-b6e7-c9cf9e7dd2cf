{"version": "2.0.0", "tasks": [{"type": "cppbuild", "label": "C/C++: gcc.exe 生成活动文件", "command": "C:\\msys64\\mingw64\\bin\\gcc.exe", "args": ["-fdiagnostics-color=always", "-g", "${file}", "-o", "${fileDirname}\\${fileBasenameNoExtension}.exe"], "options": {"cwd": "${fileDirname}"}, "problemMatcher": ["$gcc"], "group": {"kind": "build", "isDefault": true}, "detail": "调试器生成的任务。"}, {"type": "shell", "label": "编译当前C文件", "command": "gcc", "args": ["-g", "-Wall", "${file}", "-o", "${fileDirname}\\${fileBasenameNoExtension}.exe"], "group": {"kind": "build", "isDefault": false}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": "$gcc"}, {"type": "shell", "label": "运行当前C程序", "command": "${fileDirname}\\${fileBasenameNoExtension}.exe", "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": true, "panel": "shared"}, "dependsOn": "编译当前C文件"}]}