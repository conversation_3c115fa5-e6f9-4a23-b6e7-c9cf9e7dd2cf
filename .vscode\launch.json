{"version": "0.2.0", "configurations": [{"name": "(gdb) 启动", "type": "cppdbg", "request": "launch", "program": "C:\\Users\\<USER>\\Documents\\C/test.exe", "args": [], "stopAtEntry": false, "cwd": "${fileDirname}", "environment": [], "externalConsole": false, "MIMode": "gdb", "miDebuggerPath": "C:\\msys64\\mingw64\\bin\\gdb.exe", "setupCommands": [{"description": "为 gdb 启用整齐打印", "text": "-enable-pretty-printing", "ignoreFailures": true}, {"description": "将反汇编风格设置为 Intel", "text": "-gdb-set disassembly-flavor intel", "ignoreFailures": true}]}, {"name": "(gdb) Windows 上的 Bash 启动", "type": "cppdbg", "request": "launch", "program": "输入程序名称，例如 ${workspaceFolder}/a.exe", "args": [], "stopAtEntry": false, "cwd": "${fileDirname}", "environment": [], "externalConsole": false, "pipeTransport": {"debuggerPath": "/usr/bin/gdb", "pipeProgram": "${env:windir}\\system32\\bash.exe", "pipeArgs": ["-c"], "pipeCwd": ""}, "setupCommands": [{"description": "为 gdb 启用整齐打印", "text": "-enable-pretty-printing", "ignoreFailures": true}, {"description": "将反汇编风格设置为 Intel", "text": "-gdb-set disassembly-flavor intel", "ignoreFailures": true}]}, {"name": "(Windows) 启动", "type": "cppvsdbg", "request": "launch", "program": "输入程序名称，例如 ${workspaceFolder}/a.exe", "args": [], "stopAtEntry": false, "cwd": "${fileDirname}", "environment": [], "console": "externalTerminal"}, {"name": "(gdb) Windows 上的 Bash 启动", "type": "cppdbg", "request": "launch", "program": "输入程序名称，例如 ${workspaceFolder}/a.exe", "args": [], "stopAtEntry": false, "cwd": "${fileDirname}", "environment": [], "externalConsole": false, "pipeTransport": {"debuggerPath": "/usr/bin/gdb", "pipeProgram": "${env:windir}\\system32\\bash.exe", "pipeArgs": ["-c"], "pipeCwd": ""}, "setupCommands": [{"description": "为 gdb 启用整齐打印", "text": "-enable-pretty-printing", "ignoreFailures": true}, {"description": "将反汇编风格设置为 Intel", "text": "-gdb-set disassembly-flavor intel", "ignoreFailures": true}]}, {"name": "(Windows) 启动", "type": "cppvsdbg", "request": "launch", "program": "输入程序名称，例如 ${workspaceFolder}/a.exe", "args": [], "stopAtEntry": false, "cwd": "${fileDirname}", "environment": [], "console": "externalTerminal"}, {"name": "C/C++ Runner: Debug Session", "type": "cppdbg", "request": "launch", "args": [], "stopAtEntry": false, "externalConsole": true, "cwd": "c:/Users/<USER>/Documents/C", "program": "c:/Users/<USER>/Documents/C/build/Debug/outDebug", "MIMode": "gdb", "miDebuggerPath": "gdb", "setupCommands": [{"description": "Enable pretty-printing for gdb", "text": "-enable-pretty-printing", "ignoreFailures": true}]}]}